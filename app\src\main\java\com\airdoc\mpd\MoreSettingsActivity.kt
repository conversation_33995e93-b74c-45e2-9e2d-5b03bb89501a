package com.airdoc.mpd

import android.app.AlertDialog
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.widget.EditText
import android.widget.Toast
import androidx.activity.viewModels
import androidx.core.graphics.toColorInt
import com.airdoc.component.common.base.BaseCommonActivity
import com.airdoc.component.common.cache.MMKVManager
import com.airdoc.component.common.log.Logger
import com.airdoc.component.common.utils.PackageUtils
import com.airdoc.mpd.common.CommonPreference
import com.airdoc.mpd.databinding.ActivityMoreSettingsBinding
import com.airdoc.mpd.ppg.PPGManager
import com.airdoc.mpd.ppg.bean.AnalysisResult
import com.airdoc.mpd.ppg.bean.PPGDataPoint
import com.airdoc.mpd.ppg.vm.PpgViewModel
import com.google.gson.Gson
import com.jeremyliao.liveeventbus.LiveEventBus
import com.lepu.blepro.event.EventMsgConst
import com.lepu.blepro.event.InterfaceEvent
import com.lepu.blepro.ext.BleServiceHelper
import com.lepu.blepro.ext.pc60fw.RtParam
import com.lepu.blepro.ext.pc60fw.RtWave
import com.lepu.blepro.objs.Bluetooth
import com.lepu.blepro.objs.BluetoothController
import com.lepu.blepro.observer.BIOL
import com.lepu.blepro.observer.BleChangeObserver
import java.io.File

/**
 * FileName: MoreSettingsActivity
 * Author by lilin,Date on 2025/7/14 9:53
 * PS: Not easy to write code, please indicate.
 */
class MoreSettingsActivity : BaseCommonActivity(),BleChangeObserver {

    companion object{
        private val TAG = MoreSettingsActivity::class.java.simpleName

        fun createIntent(context: Context): Intent {
            val intent = Intent(context, MoreSettingsActivity::class.java)
            return intent
        }
    }
    private val ppgVM by viewModels<PpgViewModel>()

    private val supportedModels = intArrayOf(Bluetooth.MODEL_PC60FW)

    //数据采集是否完成
    private var isComplete = false

    //数据采集是否终止
    private var isTerminate = false

    private var mPatientId = 0L

    private val ppgDataPointList = mutableListOf<PPGDataPoint>()

    private lateinit var binding: ActivityMoreSettingsBinding

    //数据分析结果
    private var ppgAnalysisResult: AnalysisResult? = null

    private val gson = Gson()

    // PPG数据超时处理
    private val ppgTimeoutHandler = Handler(Looper.getMainLooper())
    private var ppgTimeoutRunnable: Runnable? = null
    private var lastDataReceivedTime = 0L

    // PPG数据超时时间（毫秒）- 默认30秒无数据则调用completeEvaluation
    private val PPG_DATA_TIMEOUT = 3 * 1000L

    // 是否已经开始数据采集
    private var isDataCollectionStarted = false

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityMoreSettingsBinding.inflate(layoutInflater)
        setContentView(binding.root)
        initObserver()
        initView()
    }

    private fun initObserver() {
        // 监听服务初始化完成
        LiveEventBus.get<Boolean>(EventMsgConst.Ble.EventServiceConnectedAndInterfaceInit)
            .observeSticky(this) {
                startScan()
            }

        // 监听设备发现
        LiveEventBus.get<Bluetooth>(EventMsgConst.Discovery.EventDeviceFound)
            .observe(this) { bluetooth ->
                // 处理发现的设备
                handleDeviceFound(bluetooth)
            }

        LiveEventBus.get<InterfaceEvent>(InterfaceEvent.PC60Fw.EventPC60FwRtParam).observe(this) {
            val data = it.data
            Logger.e(TAG, msg = "EventPC60FwRtParam data = $data")
            if (data is RtParam){

            }
        }


        LiveEventBus.get<InterfaceEvent>(InterfaceEvent.PC60Fw.EventPC60FwRtWave).observe(this) {
            val data = it.data
            if (data is RtWave){
                Logger.e(TAG, msg = "EventPC60FwRtWave waveIntData = ${gson.toJson(data.waveIntData)}")
                if (!isComplete && !isTerminate){
                    // 标记数据采集已开始
                    if (!isDataCollectionStarted) {
                        isDataCollectionStarted = true
                        Logger.d(TAG, msg = "PPG数据采集开始")
                    }

                    // 更新最后接收数据的时间
                    lastDataReceivedTime = System.currentTimeMillis()

                    // 重置超时计时器
                    resetPpgTimeout()

                    val ints = data.waveIntData.toList()
                    ints.forEachIndexed { index, value ->
                        if (ppgDataPointList.isEmpty()){
                            ppgDataPointList.add(PPGDataPoint(value.toDouble(), System.currentTimeMillis() * 1_000_000L + System.nanoTime() % 1_000_000L))
                        }else{
                            //间隔20毫秒，转成纳秒
                            ppgDataPointList.add(PPGDataPoint(value.toDouble(),ppgDataPointList[ppgDataPointList.size - 1].timestamp + 20 * 1_000_000L))
                        }
                    }
                }
            }
        }
    }

    private fun completeEvaluation(){
        Logger.d(TAG, msg = "开始完成PPG数据评估，数据点数量: ${ppgDataPointList.size}")
        isComplete = true
        isTerminate = false

        // 取消超时计时器
        cancelPpgTimeout()

        ppgAnalysisResult = PPGManager.analyzeECG(ppgDataPointList)?.apply {
            patientId = mPatientId
        }

        Logger.d(TAG, msg = "PPG数据评估完成，分析结果: ${ppgAnalysisResult != null}")
    }

    /**
     * 重置PPG数据超时计时器
     */
    private fun resetPpgTimeout() {
        // 取消之前的超时任务
        cancelPpgTimeout()

        // 创建新的超时任务
        ppgTimeoutRunnable = Runnable {
            Logger.w(TAG, msg = "PPG数据接收超时，自动调用completeEvaluation")
            if (!isComplete && isDataCollectionStarted) {
                completeEvaluation()
            }
        }

        // 设置超时延迟
        ppgTimeoutHandler.postDelayed(ppgTimeoutRunnable!!, PPG_DATA_TIMEOUT)
        Logger.d(TAG, msg = "PPG数据超时计时器已重置，超时时间: ${PPG_DATA_TIMEOUT}ms")
    }

    /**
     * 取消PPG数据超时计时器
     */
    private fun cancelPpgTimeout() {
        ppgTimeoutRunnable?.let {
            ppgTimeoutHandler.removeCallbacks(it)
            ppgTimeoutRunnable = null
            Logger.d(TAG, msg = "PPG数据超时计时器已取消")
        }
    }

    private fun startScan() {
        Logger.e(TAG,TAG,"开始扫描")

        BleServiceHelper.BleServiceHelper.startScan(supportedModels)
    }

    private fun handleDeviceFound(bluetooth: Bluetooth) {
        // 检查指尖式数据采集开关是否开启
        val isFingertipCollectionEnabled = MMKVManager.decodeBool(CommonPreference.ENABLE_FINGERTIP_DATA_COLLECTION) ?: true

        if (!isFingertipCollectionEnabled) {
            Logger.w(TAG, msg = "指尖式数据采集已关闭，拒绝连接PPG设备: ${bluetooth.name}")
            Toast.makeText(this, "指尖式数据采集已关闭，无法连接设备", Toast.LENGTH_SHORT).show()
            return
        }

        val devices = BluetoothController.getDevices()
        Logger.e(TAG,TAG,devices.toString())
//        updateDeviceList(devices)
        Logger.e(TAG,TAG,devices.get(0).toString())
        Logger.d(TAG, msg = "指尖式数据采集已开启，允许连接PPG设备: ${bluetooth.name}")
        connectPpgDevice(devices.get(0));
    }


    fun connectPpgDevice(bluetooth: Bluetooth){
        // set interface before connect
        BleServiceHelper.Companion.BleServiceHelper.setInterfaces(bluetooth.model)
        // add observer(ble state)
        lifecycle.addObserver(BIOL(this, intArrayOf(bluetooth.model)))

        // stop scan before connect
        BleServiceHelper.Companion.BleServiceHelper.stopScan()
        // connect
        BleServiceHelper.Companion.BleServiceHelper.connect(this, bluetooth.model, bluetooth.device)
        BluetoothController.clear()

        // 连接设备后开始超时监控
        Logger.d(TAG, msg = "PPG设备连接中，开始超时监控")
        resetPpgTimeout()
    }



    private fun initView() {
        initListener()

        // 初始化首页语音引导开关状态
        binding.switchProactivelyGreet.isChecked = MMKVManager.decodeBool(CommonPreference.ENABLE_PROACTIVE_GREETING) == true

        // 初始化指尖式数据采集开关状态
        binding.switchFingertipCollection.isChecked = MMKVManager.decodeBool(CommonPreference.ENABLE_FINGERTIP_DATA_COLLECTION) == true

        // 初始化版本信息
        binding.tvModelVersion.text = MMKVManager.decodeString(CommonPreference.MODEL_VERSION) ?: "V1.0.0.1"
        binding.tvTestVersion.text = MMKVManager.decodeString(CommonPreference.TEST_VERSION) ?: "V2.0.1"

        // 初始化采集器编号
        binding.tvCollectorNumber.text = MMKVManager.decodeString(CommonPreference.COLLECTOR_NUMBER) ?: "000477"

        // 初始化数据缓存状态
        updateDataCacheStatus()
    }

    private fun initListener() {
        // 返回按钮
        binding.ivBack.setOnClickListener {
            finish()
        }

        // 首页语音引导开关
        binding.switchProactivelyGreet.setOnCheckedChangeListener { buttonView, isChecked ->
            //不是人为点击按钮触发，不处理
            if (!buttonView.isPressed) {
                return@setOnCheckedChangeListener
            }
            MMKVManager.encodeBool(CommonPreference.ENABLE_PROACTIVE_GREETING, isChecked)
            Logger.d(TAG, msg = "首页语音引导开关状态改变: $isChecked")
        }

        // 指尖式数据采集开关
        binding.switchFingertipCollection.setOnCheckedChangeListener { buttonView, isChecked ->
            //不是人为点击按钮触发，不处理
            if (!buttonView.isPressed) {
                return@setOnCheckedChangeListener
            }
            MMKVManager.encodeBool(CommonPreference.ENABLE_FINGERTIP_DATA_COLLECTION, isChecked)
            Logger.d(TAG, msg = "指尖式数据采集开关状态改变: $isChecked")
        }

        // 模型版本更新按钮
        binding.btnUpdateModel.setOnClickListener {
            showUpdateDialog("模型版本") { newVersion ->
                MMKVManager.encodeString(CommonPreference.MODEL_VERSION, newVersion)
                binding.tvModelVersion.text = newVersion
                Logger.d(TAG, msg = "模型版本更新为: $newVersion")
            }
        }

        // 测试版版本更新按钮
        binding.btnUpdateTest.setOnClickListener {
            showUpdateDialog("测试版版本") { newVersion ->
                MMKVManager.encodeString(CommonPreference.TEST_VERSION, newVersion)
                binding.tvTestVersion.text = newVersion
                Logger.d(TAG, msg = "测试版版本更新为: $newVersion")
            }
        }

        // 数据缓存上传按钮
        binding.btnUploadCache.setOnClickListener {
            uploadDataCache()
        }

        // 采集器编号变更按钮
        binding.btnChangeCollector.setOnClickListener {
            showCollectorNumberDialog()
        }
    }

    override fun onBleStateChanged(model: Int, state: Int) {
        ppgVM.setBleState(state)
        Logger.d(TAG, msg = "蓝牙状态变化: model=$model, state=$state")

        // 如果蓝牙断开连接，取消超时计时器
        if (state == 0) { // 假设0表示断开连接
            Logger.w(TAG, msg = "蓝牙连接断开，取消PPG数据超时监控")
            cancelPpgTimeout()
            isDataCollectionStarted = false
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        // 清理超时计时器
        cancelPpgTimeout()
        Logger.d(TAG, msg = "Activity销毁，清理PPG超时计时器")
    }

    /**
     * 更新数据缓存状态
     */
    private fun updateDataCacheStatus() {
        try {
            val cacheSize = getCacheSize()
            val status = if (cacheSize > 0) {
                "${String.format("%.1f", cacheSize / 1024.0 / 1024.0)}MB"
            } else {
                getString(R.string.str_none)
            }
            binding.tvDataCacheStatus.text = status
        } catch (e: Exception) {
            Logger.e(TAG, msg = "获取缓存大小异常: ${e.message}")
            binding.tvDataCacheStatus.text = getString(R.string.str_none)
        }
    }

    /**
     * 获取缓存大小
     */
    private fun getCacheSize(): Long {
        var size = 0L
        try {
            // 计算应用缓存目录大小
            size += getDirSize(cacheDir)
            // 计算WebView缓存大小
            val webViewCacheDir = File(cacheDir, "webview")
            if (webViewCacheDir.exists()) {
                size += getDirSize(webViewCacheDir)
            }
            // 计算其他可能的缓存目录
            val webViewDataDir = File(filesDir.parent, "app_webview")
            if (webViewDataDir.exists()) {
                size += getDirSize(webViewDataDir)
            }
        } catch (e: Exception) {
            Logger.e(TAG, msg = "计算缓存大小异常: ${e.message}")
        }
        return size
    }

    /**
     * 获取目录大小
     */
    private fun getDirSize(dir: File): Long {
        var size = 0L
        try {
            if (dir.exists()) {
                dir.listFiles()?.forEach { file ->
                    size += if (file.isDirectory) {
                        getDirSize(file)
                    } else {
                        file.length()
                    }
                }
            }
        } catch (e: Exception) {
            Logger.e(TAG, msg = "获取目录大小异常: ${e.message}")
        }
        return size
    }

    /**
     * 显示版本更新对话框
     */
    private fun showUpdateDialog(title: String, onUpdate: (String) -> Unit) {
        val editText = EditText(this).apply {
            hint = "请输入新版本号，如：V1.0.0.2"
            setPadding(50, 30, 50, 30)
        }

        AlertDialog.Builder(this)
            .setTitle("更新$title")
            .setView(editText)
            .setPositiveButton("确定") { _, _ ->
                val newVersion = editText.text.toString().trim()
                if (newVersion.isNotEmpty()) {
                    if (newVersion.startsWith("V") || newVersion.startsWith("v")) {
                        onUpdate(newVersion)
                        Toast.makeText(this, "${title}更新成功", Toast.LENGTH_SHORT).show()
                    } else {
                        Toast.makeText(this, "版本号格式错误，请以V开头", Toast.LENGTH_SHORT).show()
                    }
                } else {
                    Toast.makeText(this, "版本号不能为空", Toast.LENGTH_SHORT).show()
                }
            }
            .setNegativeButton("取消", null)
            .show()
    }

    /**
     * 显示采集器编号变更对话框
     */
    private fun showCollectorNumberDialog() {
        val editText = EditText(this).apply {
            hint = "请输入新的采集器编号，如：000478"
            setText(MMKVManager.decodeString(CommonPreference.COLLECTOR_NUMBER) ?: "000477")
            setPadding(50, 30, 50, 30)
            selectAll()
        }

        AlertDialog.Builder(this)
            .setTitle("变更采集器编号")
            .setView(editText)
            .setPositiveButton("确定") { _, _ ->
                val newNumber = editText.text.toString().trim()
                if (newNumber.isNotEmpty()) {
                    if (newNumber.matches(Regex("\\d{6}"))) {
                        MMKVManager.encodeString(CommonPreference.COLLECTOR_NUMBER, newNumber)
                        binding.tvCollectorNumber.text = newNumber
                        Logger.d(TAG, msg = "采集器编号变更为: $newNumber")
                        Toast.makeText(this, "采集器编号变更成功", Toast.LENGTH_SHORT).show()
                    } else {
                        Toast.makeText(this, "编号格式错误，请输入6位数字", Toast.LENGTH_SHORT).show()
                    }
                } else {
                    Toast.makeText(this, "编号不能为空", Toast.LENGTH_SHORT).show()
                }
            }
            .setNegativeButton("取消", null)
            .show()
    }

    /**
     * 上传数据缓存
     */
    private fun uploadDataCache() {
        try {
            Logger.d(TAG, msg = "开始上传数据缓存...")

            // 这里可以添加实际的上传逻辑
            // 例如：将缓存文件打包上传到服务器

            // 模拟上传过程
            Toast.makeText(this, "数据缓存上传中...", Toast.LENGTH_SHORT).show()

            // 上传完成后清理缓存
            clearDataCache()

            // 更新缓存状态显示
            updateDataCacheStatus()

            Toast.makeText(this, "数据缓存上传完成", Toast.LENGTH_SHORT).show()
            Logger.d(TAG, msg = "数据缓存上传完成")

        } catch (e: Exception) {
            Logger.e(TAG, msg = "上传数据缓存异常: ${e.message}")
            Toast.makeText(this, "上传失败，请重试", Toast.LENGTH_SHORT).show()
        }
    }

    /**
     * 清理数据缓存
     */
    private fun clearDataCache() {
        try {
            Logger.d(TAG, msg = "开始清理数据缓存...")

            // 清理应用缓存目录
            clearDirectory(cacheDir)

            // 清理WebView缓存
            val webViewCacheDir = File(cacheDir, "webview")
            if (webViewCacheDir.exists()) {
                clearDirectory(webViewCacheDir)
            }

            // 清理WebView数据目录
            val webViewDataDir = File(filesDir.parent, "app_webview")
            if (webViewDataDir.exists()) {
                clearDirectory(webViewDataDir)
            }

            Logger.d(TAG, msg = "数据缓存清理完成")

        } catch (e: Exception) {
            Logger.e(TAG, msg = "清理数据缓存异常: ${e.message}")
        }
    }

    /**
     * 清理目录
     */
    private fun clearDirectory(dir: File) {
        try {
            if (dir.exists() && dir.isDirectory) {
                dir.listFiles()?.forEach { file ->
                    if (file.isDirectory) {
                        clearDirectory(file)
                    }
                    file.delete()
                }
            }
        } catch (e: Exception) {
            Logger.e(TAG, msg = "清理目录异常: ${e.message}")
        }
    }

}